﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Exceptions;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.Models.Identity;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Services.Rules.Shared;
using WHO.MALARIA.Services.Rules.User;

namespace WHO.MALARIA.Services.Handlers.Commands
{
    /// <summary>
    /// Handles the UpdateUserStatusByWhoUserCommand and changes the user's status by WHO user
    /// </summary>
    public class UpdateUserStatusByWhoUserCommandHandler : RuleBase, IRequestHandler<UpdateUserStatusByWhoUserCommand, bool>
    {
        #region Variable Declaration

        private readonly IUnitOfWork _unitOfWork;
        private readonly ICommonRuleChecker _commonRuleChecker;
        private readonly IUserRuleChecker _userRuleChecker;
        private readonly ITranslationService _translationService;
        private readonly ILogger<UpdateUserStatusByWhoUserCommandHandler> _logger;

        #endregion

        #region Constructor
        public UpdateUserStatusByWhoUserCommandHandler(
            IUnitOfWork unitOfWork,
            ICommonRuleChecker commonRuleChecker,
            IUserRuleChecker userRuleChecker,
            ITranslationService translationService,
            ILogger<UpdateUserStatusByWhoUserCommandHandler> logger)
        {
            _unitOfWork = unitOfWork;
            _commonRuleChecker = commonRuleChecker;
            _userRuleChecker = userRuleChecker;
            _translationService = translationService;
            _logger = logger;
        }
        #endregion

        #region Command Handler

        /// <summary>
        /// Change user status
        /// </summary>
        public async Task<bool> Handle(UpdateUserStatusByWhoUserCommand request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Starting user status update for user {UserId} by WHO user {CurrentUserId}. IsActive: {IsActive}, UserType: {UserType}, CountryId: {CountryId}",
                request.UserId, request.CurrentUserId, request.IsActive, request.UserType, request.CountryId);

            //Check business rules
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, request.UserId, nameof(request.UserId)));
            CheckRule(new UserShouldBeWhoAdminRule(_translationService, _userRuleChecker, request.CurrentUserId));

            User user = await _unitOfWork.UserRepository.Queryable(u => u.Id == request.UserId)
                                                      .Include(u => u.Identity)
                                                      .Include(u => u.UserCountryAccesses)
                                                      .FirstOrDefaultAsync();

            // Check when who user try to active any super manager. 
            if (request.CountryId.HasValue && request.IsActive)
            {
                CheckRule(new HasDifferentSuperManagerAssignedToCountryRule(_translationService, _userRuleChecker, request.UserId, request.CountryId.Value));
            }

            // Check if a user is assigned as a manager to any in-progress assessment when the who admin user is deactivated by another who admin
            if (user.IsWhoAdmin && !request.IsActive)
            {
                CheckRule(new UserIsManagerOfAnyInProgressAssessment(_translationService, _userRuleChecker, request.UserId));
            }

            if (user == null)
            {
                throw new RecordNotFoundException(request.UserId, "User");
            }

            // If the user type is "super manager" and they send a request to "activate or deactivate" we can update only that specific user type related record
            if (request.UserType == (int)UserRoleEnum.SuperManager)
            {
                UserCountryAccess userCountryAccess = user.UserCountryAccesses.FirstOrDefault(u => u.CountryId == request.CountryId && u.UserType == request.UserType);
                userCountryAccess.Status = (int)(request.IsActive ? UserCountryAccessRightsEnum.Accepted : UserCountryAccessRightsEnum.Pending);
                _unitOfWork.UserCountryAccessRepository.Update(userCountryAccess);

                // If only one country has access to that user, we can also update records from the User and Identity tables.
                if (user.UserCountryAccesses.Count == 1)
                {
                    user.Status = (int)(request.IsActive ? UserStatus.Active : UserStatus.InActive);
                    _unitOfWork.UserRepository.Update(user);

                    user.Identity.Status = request.IsActive;
                    _unitOfWork.IdentityRepository.Update(user.Identity);
                }               
            }
            else 
            {
                user.Status = (int)(request.IsActive ? UserStatus.Active : UserStatus.InActive);
                _unitOfWork.UserRepository.Update(user);

                user.Identity.Status = request.IsActive;
                _unitOfWork.IdentityRepository.Update(user.Identity);

                foreach (UserCountryAccess userCountryAccess in user.UserCountryAccesses)
                {
                    userCountryAccess.Status = (int)(request.IsActive ? UserCountryAccessRightsEnum.Accepted : UserCountryAccessRightsEnum.Pending);
                    _unitOfWork.UserCountryAccessRepository.Update(userCountryAccess);
                }
            }

            _logger.LogInformation("Committing database changes for user {UserId} status update", request.UserId);

            if (await _unitOfWork.CommitAsync(cancellationToken) == 0)
            {
                _logger.LogError("Database commit failed for user {UserId} status update", request.UserId);
                throw new ApplicationException();
            }

            _logger.LogInformation("User {UserId} status update completed successfully by WHO user {CurrentUserId}",
                request.UserId, request.CurrentUserId);

            return true;
        }
        #endregion
    }
}
