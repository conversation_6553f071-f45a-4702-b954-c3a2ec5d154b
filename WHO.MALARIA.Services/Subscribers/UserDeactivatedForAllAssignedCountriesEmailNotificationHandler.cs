﻿using System.Threading;
using System.Threading.Tasks;

using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Events;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Features;
using WHO.MALARIA.Features.Helpers;
using WHO.MALARIA.Features.Models.Email;

namespace WHO.MALARIA.Services.Subscribers
{
    /// <summary>
    /// Handles the notification to be sent to user when He/She is deactivated from the system
    /// </summary>
    public class UserDeactivatedForAllAssignedCountriesEmailNotificationHandler : INotificationHandler<UserDeactivatedForAllAssignedCountriesEmailNotification>
    {
        private readonly ILogger<UserDeactivatedForAllAssignedCountriesEmailNotificationHandler> _logger;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IEmailService _emailService;

        public UserDeactivatedForAllAssignedCountriesEmailNotificationHandler(ILogger<UserDeactivatedForAllAssignedCountriesEmailNotificationHandler> logger, IUnitOfWork unitOfWork, IEmailService emailService)
        {
            _logger = logger;
            _unitOfWork = unitOfWork;
            _emailService = emailService;
        }

        /// <summary>
        /// Fetches the email template and sends the email to the user
        /// </summary>
        /// <param name="notification">Object of UserDeactivatedForAllAssignedCountriesEmailNotification class</param>
        /// <param name="cancellationToken">Used to cancel the current operation</param>
        public async Task Handle(UserDeactivatedForAllAssignedCountriesEmailNotification notification, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation(Constants.SeriLog.InformationLogMessageTemplate, this.GetType().Name, UtilityHelper.GetCallerMethodName(), notification);

                EmailTemplate emailTemplate = await _unitOfWork.EmailTemplateRepository
                                                 .Queryable(et => et.Type == (int)EmailTemplateType.DeactivatedUserForAllAssignedCountries)
                                                 .SingleAsync();

                await _emailService.SendEmail(new string[] { notification.Email }, emailTemplate.Subject, emailTemplate.Body);

                _logger.LogInformation("User deactivated for all assigned countries email sent successfully to {Email}", notification.Email);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send user deactivated for all assigned countries email to {Email}. Error: {Message}",
                    notification.Email, ex.Message);
                // Don't rethrow - email failure should not block the main operation
            }
        }
    }
}
