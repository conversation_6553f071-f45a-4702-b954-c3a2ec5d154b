﻿using System;
using System.Threading;
using System.Threading.Tasks;

using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Events;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Features;
using WHO.MALARIA.Features.Helpers;
using WHO.MALARIA.Features.Models.Email;

namespace WHO.MALARIA.Services.Subscribers
{
    /// <summary>
    /// Handles the notification to be sent to user when user's access changes on the assigned country.
    /// </summary>
    public class UserCountryAccessRightChangeEmailNotificationHandler : INotificationHandler<UserCountryAccessRightChangeEmailNotification>
    {
        private readonly IEmailService _emailService;
        private readonly ILogger<UserCountryAccessRightChangeEmailNotification> _logger;
        private readonly IUnitOfWork _unitOfWork;

        public UserCountryAccessRightChangeEmailNotificationHandler(IEmailService emailService,
                                                       ILogger<UserCountryAccessRightChangeEmailNotification> logger,
                                                       IUnitOfWork unitOfWork)
        {
            _emailService = emailService;
            _logger = logger;
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// Fetches the email template, formats the email body and sends the email to the user
        /// </summary>
        /// <param name="notification">Object of UserCountryAccessRightChangeEmailNotification class</param>
        /// <param name="cancellationToken">Used to cancel the current operation</param>
        public async Task Handle(UserCountryAccessRightChangeEmailNotification notification, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation(Constants.SeriLog.InformationLogMessageTemplate, this.GetType().Name, UtilityHelper.GetCallerMethodName(), notification);

                EmailTemplateType emailTemplateType = notification.UserCountryAccess == UserCountryAccessRightsEnum.InActive
                    ? EmailTemplateType.DeactivateViewerUser : EmailTemplateType.ActivateViewerUser;

                EmailTemplate emailTemplate = await _unitOfWork.EmailTemplateRepository
                                                 .Queryable(et => et.Type == (int)emailTemplateType)
                                                 .SingleAsync();

                string status = notification.UserCountryAccess == UserCountryAccessRightsEnum.Accepted ? "Active" : notification.UserCountryAccess.ToString();

                string role = EnumHelper.GetDisplayUserType((int)notification.UserType);

                string body = string.Format(emailTemplate.Body,
                                            notification.Country,
                                            role,
                                            status,
                                            role);

                await _emailService.SendEmail(new string[] { notification.Email }, emailTemplate.Subject, body);

                _logger.LogInformation("User country access right change email sent successfully to {Email} for country {Country}",
                    notification.Email, notification.Country);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send user country access right change email to {Email} for country {Country}. Error: {Message}",
                    notification.Email, notification.Country, ex.Message);
                // Don't rethrow - email failure should not block the main operation
            }
        }
    }
}
